import asyncio

from library.playstation_library.helpers.auth_edit import Authenticator
from library.playstation_library.helpers.async_request_builder import AsyncRequestBuilder
from library.playstation_library.helpers.proxy_handler_client import ProxySession
from library.playstation_library.helpers.utils import get_proxies, get_tokens, get_panel
import query_builder as qb
import random
from datetime import datetime, timedelta, timezone
import isodate

from logging_utils import init_logger, logging

logger = init_logger()

ps_db = qb.db(db_name='ps')
ps_public = qb.Schema('public', db_name='ps')
ps_metrics = qb.Schema('metrics', db_name='ps')

NUMBER_OF_TOKENS = 1

async def get_library(builder: AsyncRequestBuilder, account_id: int, sessions=None, retries=3):
    base_uri = f'https://m.np.playstation.com/api/gamelist/v2/users/{account_id}/titles'
    params = {
        'categories': 'ps4_game,ps5_native_game',
        'limit': 200,   # max allowed
        'offset': 0
    }

    all_titles = []

    while True:
        session = random.choice(sessions)
        for attempt in range(retries):
            try:
                response = await builder.get(url=base_uri, session=session, params=params)
                break
            except Exception:
                if attempt == retries - 1:
                    raise
                await asyncio.sleep(1)

        items = response.get("titles", [])
        all_titles.extend(items)

        if len(items) < params["limit"]:
            break

        params["offset"] += params["limit"]

    return all_titles

async def format_ps_api_response(data):
    """
    Formats PlayStation API response into a simplified structure.
    """
    formatted_games = []

    for game in data:
        # Extract main fields
        title_id = game.get("titleId")
        name = game.get("name")
        category = game.get("category")
        genres = game.get("concept", {}).get("genres", [])
        country = game.get("concept", {}).get("country")
        language = game.get("concept", {}).get("language")
        play_count = game.get("playCount", 0)
        first_played = game.get("firstPlayedDateTime")
        last_played = game.get("lastPlayedDateTime")
        
        # Convert ISO 8601 duration to total seconds
        play_duration_iso = game.get("playDuration", "PT0S")
        play_duration_seconds = int(isodate.parse_duration(play_duration_iso).total_seconds())
        play_duration_str = str(timedelta(seconds=play_duration_seconds))

        formatted_games.append({
            "title_id": title_id,
            "name": name,
            "category": category,
            "play_count": play_count,
            "first_played": first_played,
            "last_played": last_played,
            "play_duration": play_duration_str,
        })

    return formatted_games

async def run(panel):
    try:
        proxy_pool = await get_proxies()

        tokens = await get_tokens(number_of_tokens=NUMBER_OF_TOKENS)
        print(f"Got {len(tokens)} tokens")

        builders = []
        for i, token in enumerate(tokens):
            try:
                authenticator = await Authenticator.async_init(npsso_token=token, proxy_pool=proxy_pool, logger=logger)
                if authenticator.success:
                    builder = AsyncRequestBuilder(authenticator)
                    builders.append(builder)
                    print(f"Successfully created builder {i+1}")
                else:
                    print(f"Failed to authenticate token {i+1}")
            except Exception as e:
                print(f"Error creating authenticator {i+1}: {e}")

        print(f"Created {len(builders)} working builders")

        if not builders:
            print("No working builders available!")
            return
        
        response = await get_library(builders[0], 104192958940080447, proxy_pool)
        formatted_response = await format_ps_api_response(response)
        for f in formatted_response:
            for k, v in f.items():
                print(f"{k}: {v}")
            print("-----")

    except Exception as e:
        print(f"Error: {e}")
    finally:
        if 'proxy_pool' in locals() and proxy_pool:
            for session in proxy_pool:
                try:
                    await session.close()
                except Exception as e:
                    print(f"Error closing session: {e}")

async def main():
    import time
    time_0 = time.time()
    print("Starting")

    panel = await get_panel()
    print(len(panel))
    panel = panel[:100]
    await run(panel)
    
    time_1 = time.time()
    print(f"Time: {time_1 - time_0}")

if __name__ == "__main__":
    asyncio.run(main())








